package dev.pika.betterHUB.utils;

import dev.pika.betterHUB.BetterHUB;
import net.md_5.bungee.config.Configuration;
import net.md_5.bungee.config.ConfigurationProvider;
import net.md_5.bungee.config.YamlConfiguration;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

public class ConfigUpdater {
    
    private final BetterHUB plugin;
    private static final int CURRENT_CONFIG_VERSION = 1;
    
    public ConfigUpdater(BetterHUB plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 检查并更新配置文件中缺失的配置项
     * 
     * @return 是否有更新配置
     */
    public boolean updateConfig() {
        File configFile = new File(plugin.getDataFolder(), "config.yml");
        if (!configFile.exists()) {
            plugin.getLogger().warning("配置文件不存在，无法更新!");
            return false;
        }
        
        // 创建备份文件
        File backupFile = new File(plugin.getDataFolder(), "config.yml.bak");
        try {
            Files.copy(configFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            plugin.getLogger().warning("无法创建配置文件备份: " + e.getMessage());
            // 继续执行，即使备份失败
        }
        
        try {
            // 加载当前配置
            Configuration currentConfig = ConfigurationProvider.getProvider(YamlConfiguration.class)
                    .load(configFile);
            
            // 加载默认配置
            InputStream defaultConfigStream = plugin.getResourceAsStream("config.yml");
            if (defaultConfigStream == null) {
                plugin.getLogger().warning("无法获取默认配置文件!");
                return false;
            }
            
            Configuration defaultConfig = ConfigurationProvider.getProvider(YamlConfiguration.class)
                    .load(new InputStreamReader(defaultConfigStream, StandardCharsets.UTF_8));
            
            // 检查配置版本
            int currentVersion = currentConfig.getInt("config-version", 0);
            boolean updated = false;
            
            if (currentVersion < CURRENT_CONFIG_VERSION) {
                plugin.getLogger().info("检测到配置文件版本过旧 (当前: " + currentVersion + 
                    ", 最新: " + CURRENT_CONFIG_VERSION + ")，正在更新...");
                
                // 合并配置
                updated = mergeConfigurations(currentConfig, defaultConfig, "");
                
                // 更新配置版本号
                currentConfig.set("config-version", CURRENT_CONFIG_VERSION);
                updated = true;
                
                // 如果有更新，保存配置
                if (updated) {
                    try {
                        ConfigurationProvider.getProvider(YamlConfiguration.class)
                                .save(currentConfig, configFile);
                        plugin.getLogger().info("配置文件已更新到版本 " + CURRENT_CONFIG_VERSION + 
                            "，新的配置项已添加。");
                    } catch (IOException e) {
                        plugin.getLogger().severe("保存更新后的配置文件时出错: " + e.getMessage());
                        
                        // 尝试恢复备份
                        try {
                            if (backupFile.exists()) {
                                Files.copy(backupFile.toPath(), configFile.toPath(), 
                                    StandardCopyOption.REPLACE_EXISTING);
                                plugin.getLogger().info("已恢复配置文件备份。");
                            }
                        } catch (IOException ex) {
                            plugin.getLogger().severe("恢复配置文件备份时出错: " + ex.getMessage());
                        }
                        
                        return false;
                    }
                }
            } else {
                plugin.getLogger().info("配置文件已是最新版本 (" + currentVersion + ")，无需更新。");
            }
            
            // 尝试删除备份文件（如果更新成功）
            if (backupFile.exists() && updated) {
                try {
                    Files.delete(backupFile.toPath());
                } catch (IOException e) {
                    // 忽略删除备份文件的错误
                }
            }
            
            return updated;
            
        } catch (IOException e) {
            plugin.getLogger().severe("更新配置文件时发生错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 递归合并配置，将默认配置中缺失的项添加到当前配置中
     * 
     * @param target 目标配置（当前配置）
     * @param source 源配置（默认配置）
     * @param parentPath 父路径
     * @return 是否有更新
     */
    private boolean mergeConfigurations(Configuration target, Configuration source, String parentPath) {
        boolean updated = false;

        Collection<String> sourceKeysCollection = source.getKeys();
        Set<String> sourceKeys = new HashSet<>(sourceKeysCollection);
        for (String key : sourceKeys) {
            String currentPath = parentPath.isEmpty() ? key : parentPath + "." + key;
            
            if (!target.contains(key)) {
                // 如果目标中不存在此键，直接添加
                target.set(key, source.get(key));
                plugin.getLogger().info("添加配置项: " + currentPath);
                updated = true;
            } else {
                // 如果键存在，检查是否为配置节
                Object sourceValue = source.get(key);
                Object targetValue = target.get(key);
                
                if (sourceValue instanceof Configuration && targetValue instanceof Configuration) {
                    // 如果都是配置节，递归合并
                    Configuration sourceSection = (Configuration) sourceValue;
                    Configuration targetSection = (Configuration) targetValue;
                    boolean sectionUpdated = mergeConfigurations(targetSection, sourceSection, currentPath);
                    if (sectionUpdated) {
                        updated = true;
                    }
                }
                // 如果目标中已存在此键且不是配置节，保留用户设置的值
            }
        }
        
        return updated;
    }
}
