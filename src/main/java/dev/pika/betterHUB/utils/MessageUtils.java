package dev.pika.betterHUB.utils;

import dev.pika.betterHUB.config.ConfigManager;
import net.md_5.bungee.api.ChatColor;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.ProxyServer;
import net.md_5.bungee.api.Title;
import net.md_5.bungee.api.chat.TextComponent;
import net.md_5.bungee.api.connection.ProxiedPlayer;

public class MessageUtils {

    /**
     * 将颜色代码转换为ChatColor
     * @param message 包含颜色代码的消息
     * @return 转换后的消息
     */
    public static String colorize(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * 发送聊天栏消息
     * @param player 玩家
     * @param message 消息内容
     */
    public static void sendChatMessage(ProxiedPlayer player, String message) {
        if (message != null && !message.isEmpty()) {
            player.sendMessage(new TextComponent(colorize(message)));
        }
    }

    /**
     * 发送带前缀的聊天栏消息
     * @param player 玩家
     * @param message 消息内容
     * @param configManager 配置管理器
     */
    public static void sendChatMessageWithPrefix(ProxiedPlayer player, String message, ConfigManager configManager) {
        if (message != null && !message.isEmpty()) {
            String finalMessage = message;
            if (configManager.isPrefixEnabled()) {
                finalMessage = configManager.getPrefix() + message;
            }
            player.sendMessage(new TextComponent(colorize(finalMessage)));
        }
    }

    /**
     * 发送Title消息
     * @param player 玩家
     * @param titleText 标题文本
     * @param subtitleText 副标题文本
     * @param fadeIn 淡入时间
     * @param stay 停留时间
     * @param fadeOut 淡出时间
     */
    public static void sendTitle(ProxiedPlayer player, String titleText, String subtitleText,
                                int fadeIn, int stay, int fadeOut) {
        Title title = ProxyServer.getInstance().createTitle();

        if (titleText != null && !titleText.isEmpty()) {
            title.title(new TextComponent(colorize(titleText)));
        }

        if (subtitleText != null && !subtitleText.isEmpty()) {
            title.subTitle(new TextComponent(colorize(subtitleText)));
        }

        title.fadeIn(fadeIn);
        title.stay(stay);
        title.fadeOut(fadeOut);

        title.send(player);
    }

    /**
     * 发送只有标题的Title消息
     * @param player 玩家
     * @param titleText 标题文本
     * @param fadeIn 淡入时间
     * @param stay 停留时间
     * @param fadeOut 淡出时间
     */
    public static void sendTitleOnly(ProxiedPlayer player, String titleText, 
                                    int fadeIn, int stay, int fadeOut) {
        sendTitle(player, titleText, null, fadeIn, stay, fadeOut);
    }

    /**
     * 发送只有副标题的Title消息
     * @param player 玩家
     * @param subtitleText 副标题文本
     * @param fadeIn 淡入时间
     * @param stay 停留时间
     * @param fadeOut 淡出时间
     */
    public static void sendSubtitleOnly(ProxiedPlayer player, String subtitleText, 
                                       int fadeIn, int stay, int fadeOut) {
        sendTitle(player, null, subtitleText, fadeIn, stay, fadeOut);
    }

    /**
     * 清除玩家的Title
     * @param player 玩家
     */
    public static void clearTitle(ProxiedPlayer player) {
        Title title = ProxyServer.getInstance().createTitle();
        title.clear();
        title.send(player);
    }

    /**
     * 发送Action Bar消息
     * @param player 玩家
     * @param message 消息内容
     */
    public static void sendActionBar(ProxiedPlayer player, String message) {
        if (message != null && !message.isEmpty()) {
            player.sendMessage(ChatMessageType.ACTION_BAR, new TextComponent(colorize(message)));
        }
    }
}
