package dev.pika.betterHUB.config;

import net.md_5.bungee.config.Configuration;
import net.md_5.bungee.config.ConfigurationProvider;
import net.md_5.bungee.config.YamlConfiguration;
import dev.pika.betterHUB.BetterHUB;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.List;

public class ConfigManager {
    private final BetterHUB plugin;
    private Configuration config;
    private File configFile;

    public ConfigManager(BetterHUB plugin) {
        this.plugin = plugin;
        this.configFile = new File(plugin.getDataFolder(), "config.yml");
        loadConfig();
    }

    public void loadConfig() {
        try {
            // 如果配置文件不存在，创建默认配置文件
            if (!configFile.exists()) {
                plugin.getDataFolder().mkdirs();
                try (InputStream in = plugin.getResourceAsStream("config.yml")) {
                    Files.copy(in, configFile.toPath());
                }
            }
            
            // 加载配置文件
            config = ConfigurationProvider.getProvider(YamlConfiguration.class)
                    .load(configFile);
                    
        } catch (IOException e) {
            plugin.getLogger().severe("无法加载配置文件: " + e.getMessage());
        }
    }

    public void saveConfig() {
        try {
            ConfigurationProvider.getProvider(YamlConfiguration.class)
                    .save(config, configFile);
        } catch (IOException e) {
            plugin.getLogger().severe("无法保存配置文件: " + e.getMessage());
        }
    }

    public void reloadConfig() {
        loadConfig();
    }

    // 获取大厅服务器名称
    public String getHubServerName() {
        return config.getString("hub.server-name", "lobby");
    }

    // 获取是否启用自定义指令
    public boolean isCommandsEnabled() {
        return config.getBoolean("commands.enabled", true);
    }

    // 获取自定义指令列表
    public List<String> getCommandAliases() {
        return config.getStringList("commands.aliases");
    }

    // 前缀设置
    public boolean isPrefixEnabled() {
        return config.getBoolean("messages.prefix.enabled", true);
    }

    public String getPrefix() {
        return config.getString("messages.prefix.text", "&8[&aBetterHUB&8]&7 ");
    }

    // Title消息设置
    public boolean isTitleEnabled() {
        return config.getBoolean("messages.title.enabled", true);
    }

    public String getTitleText() {
        return config.getString("messages.title.text", "&a&l欢迎回到大厅");
    }

    public int getTitleFadeIn() {
        return config.getInt("messages.title.fade-in", 10);
    }

    public int getTitleStay() {
        return config.getInt("messages.title.stay", 60);
    }

    public int getTitleFadeOut() {
        return config.getInt("messages.title.fade-out", 10);
    }

    // Subtitle消息设置
    public boolean isSubtitleEnabled() {
        return config.getBoolean("messages.subtitle.enabled", true);
    }

    public String getSubtitleText() {
        return config.getString("messages.subtitle.text", "&7享受你的游戏时光！");
    }

    public int getSubtitleFadeIn() {
        return config.getInt("messages.subtitle.fade-in", 10);
    }

    public int getSubtitleStay() {
        return config.getInt("messages.subtitle.stay", 60);
    }

    public int getSubtitleFadeOut() {
        return config.getInt("messages.subtitle.fade-out", 10);
    }

    // 聊天栏消息设置
    public boolean isChatEnabled() {
        return config.getBoolean("messages.chat.enabled", true);
    }

    public String getChatText() {
        return config.getString("messages.chat.text", "你已成功传送到大厅！");
    }

    // 错误消息
    public String getServerNotFoundMessage() {
        return config.getString("messages.error.server-not-found", "&c错误：找不到大厅服务器！");
    }

    public String getTeleportFailedMessage() {
        return config.getString("messages.error.teleport-failed", "&c传送失败，请稍后再试！");
    }

    public String getRejoiningHubMessage() {
        return config.getString("messages.error.rejoining-hub", "&e正在重新加入大厅...");
    }

    public String getAlreadyInHubMessage() {
        return config.getString("messages.already-in-hub", "&a你已经回到大厅了！");
    }

    // 其他设置
    public boolean isCheckCurrentServer() {
        return config.getBoolean("settings.check-current-server", true);
    }

    public int getCooldown() {
        return config.getInt("settings.cooldown", 3);
    }

    public boolean isDebugEnabled() {
        return config.getBoolean("settings.debug", false);
    }

    // 获取配置版本
    public int getConfigVersion() {
        return config.getInt("config-version", 0);
    }

    // 高级设置

    public boolean isLogTeleportsEnabled() {
        return config.getBoolean("advanced.log-teleports", false);
    }

    public int getMaxDistance() {
        return config.getInt("advanced.max-distance", -1);
    }
}
