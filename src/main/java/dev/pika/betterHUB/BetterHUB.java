package dev.pika.betterHUB;

import dev.pika.betterHUB.commands.BetterHubAdminCommand;
import dev.pika.betterHUB.commands.HubCommand;
import dev.pika.betterHUB.config.ConfigManager;
import dev.pika.betterHUB.utils.ConfigUpdater;
import net.md_5.bungee.api.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public final class BetterHUB extends Plugin {
    private ConfigManager configManager;
    private ConfigUpdater configUpdater;
    private List<HubCommand> hubCommands;

    @Override
    public void onEnable() {
        // 确保数据文件夹存在
        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }

        // 创建默认配置文件（如果不存在）
        saveDefaultConfig();

        // 初始化配置更新器
        configUpdater = new ConfigUpdater(this);

        // 更新配置文件（添加新的配置项）
        if (configUpdater.updateConfig()) {
            getLogger().info("配置文件已更新，正在重新加载...");
        }

        // 初始化配置管理器
        configManager = new ConfigManager(this);

        // 初始化指令列表
        hubCommands = new ArrayList<>();

        // 注册指令
        registerCommands();

        // 注册管理员指令
        getProxy().getPluginManager().registerCommand(this, new BetterHubAdminCommand(this));

        // 启动清理任务
        startCleanupTask();

        getLogger().info("BetterHUB 插件已启用！");
        getLogger().info("大厅服务器: " + configManager.getHubServerName());
        getLogger().info("已注册 " + hubCommands.size() + " 个指令");
    }

    @Override
    public void onDisable() {
        // 取消所有任务
        getProxy().getScheduler().cancel(this);

        getLogger().info("BetterHUB 插件已禁用！");
    }

    /**
     * 注册指令
     */
    private void registerCommands() {
        if (!configManager.isCommandsEnabled()) {
            getLogger().info("自定义指令已禁用");
            return;
        }

        List<String> aliases = configManager.getCommandAliases();
        if (aliases == null || aliases.isEmpty()) {
            getLogger().warning("未配置任何指令别名");
            return;
        }

        for (String alias : aliases) {
            if (alias != null && !alias.trim().isEmpty()) {
                HubCommand command = new HubCommand(this, configManager, alias.trim());
                getProxy().getPluginManager().registerCommand(this, command);
                hubCommands.add(command);
                getLogger().info("已注册指令: /" + alias.trim());
            }
        }
    }

    /**
     * 启动清理任务
     */
    private void startCleanupTask() {
        // 每5分钟清理一次冷却时间数据
        getProxy().getScheduler().schedule(this, () -> {
            for (HubCommand command : hubCommands) {
                command.cleanupCooldowns();
            }
        }, 5, 5, TimeUnit.MINUTES);
    }

    /**
     * 创建默认配置文件
     */
    private void saveDefaultConfig() {
        File configFile = new File(getDataFolder(), "config.yml");
        if (!configFile.exists()) {
            try (InputStream in = getResourceAsStream("config.yml")) {
                if (in != null) {
                    Files.copy(in, configFile.toPath());
                    getLogger().info("已创建默认配置文件");
                } else {
                    getLogger().severe("无法找到默认配置文件资源");
                }
            } catch (IOException e) {
                getLogger().severe("创建默认配置文件时发生错误: " + e.getMessage());
            }
        }
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        getLogger().info("正在重新加载配置文件...");

        // 先更新配置文件（添加新的配置项）
        if (configUpdater != null && configUpdater.updateConfig()) {
            getLogger().info("配置文件已更新，包含新的配置项。");
        }

        // 重新初始化配置管理器
        configManager = new ConfigManager(this);

        // 重新注册指令
        for (HubCommand command : hubCommands) {
            getProxy().getPluginManager().unregisterCommand(command);
        }
        hubCommands.clear();

        registerCommands();

        getLogger().info("配置文件重新加载完成。");
    }

    /**
     * 获取配置管理器
     */
    public ConfigManager getConfigManager() {
        return configManager;
    }
}
