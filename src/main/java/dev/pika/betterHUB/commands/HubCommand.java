package dev.pika.betterHUB.commands;

import dev.pika.betterHUB.BetterHUB;
import dev.pika.betterHUB.config.ConfigManager;
import dev.pika.betterHUB.utils.MessageUtils;
import net.md_5.bungee.api.CommandSender;
import net.md_5.bungee.api.config.ServerInfo;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.api.plugin.Command;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

public class HubCommand extends Command {
    private final BetterHUB plugin;
    private final ConfigManager configManager;
    private final Map<UUID, Long> cooldowns;

    public HubCommand(BetterHUB plugin, ConfigManager configManager, String name) {
        super(name);
        this.plugin = plugin;
        this.configManager = configManager;
        this.cooldowns = new HashMap<>();
    }

    @Override
    public void execute(CommandSender sender, String[] args) {
        // 检查是否为玩家
        if (!(sender instanceof ProxiedPlayer)) {
            sender.sendMessage("§c此命令只能由玩家执行！");
            return;
        }

        ProxiedPlayer player = (ProxiedPlayer) sender;

        // 检查冷却时间
        if (isOnCooldown(player)) {
            long remainingTime = getRemainingCooldown(player);
            String cooldownMessage = "&c请等待 " + remainingTime + " 秒后再使用此命令！";
            MessageUtils.sendChatMessageWithPrefix(player, cooldownMessage, configManager);
            return;
        }

        // 获取大厅服务器信息
        String hubServerName = configManager.getHubServerName();
        ServerInfo hubServer = plugin.getProxy().getServerInfo(hubServerName);

        if (hubServer == null) {
            MessageUtils.sendChatMessageWithPrefix(player, configManager.getServerNotFoundMessage(), configManager);
            if (configManager.isDebugEnabled()) {
                plugin.getLogger().warning("找不到大厅服务器: " + hubServerName);
            }
            return;
        }

        // 检查玩家是否已在大厅服务器
        boolean isInHub = configManager.isCheckCurrentServer() &&
            player.getServer() != null &&
            player.getServer().getInfo().getName().equals(hubServerName);

        if (isInHub) {
            // 如果已经在大厅，显示已回到大厅的消息，不进行传送
            MessageUtils.sendChatMessageWithPrefix(player, configManager.getAlreadyInHubMessage(), configManager);
            if (configManager.isDebugEnabled()) {
                plugin.getLogger().info("玩家 " + player.getName() + " 已经在大厅服务器中");
            }
            return;
        }

        // 设置冷却时间
        setCooldown(player);

        // 传送玩家到大厅
        player.connect(hubServer, (result, error) -> {
            if (result) {
                // 传送成功，发送消息
                plugin.getProxy().getScheduler().schedule(plugin, () -> {
                    sendWelcomeMessages(player, false);
                }, 500, TimeUnit.MILLISECONDS); // 延迟500ms发送消息，确保玩家已完全传送

                if (configManager.isDebugEnabled()) {
                    plugin.getLogger().info("玩家 " + player.getName() + " 已传送到大厅服务器");
                }
            } else {
                // 传送失败
                MessageUtils.sendChatMessageWithPrefix(player, configManager.getTeleportFailedMessage(), configManager);
                if (configManager.isDebugEnabled()) {
                    plugin.getLogger().warning("玩家 " + player.getName() + " 传送到大厅失败: " +
                        (error != null ? error.getMessage() : "未知错误"));
                }
            }
        });
    }

    /**
     * 发送欢迎消息
     * @param player 玩家
     * @param isRejoining 是否为重新加入
     */
    private void sendWelcomeMessages(ProxiedPlayer player, boolean isRejoining) {
        // 发送聊天栏消息
        if (configManager.isChatEnabled()) {
            MessageUtils.sendChatMessageWithPrefix(player, configManager.getChatText(), configManager);
        }

        // 发送Title和Subtitle
        boolean titleEnabled = configManager.isTitleEnabled();
        boolean subtitleEnabled = configManager.isSubtitleEnabled();

        if (titleEnabled || subtitleEnabled) {
            String titleText = titleEnabled ? configManager.getTitleText() : null;
            String subtitleText = subtitleEnabled ? configManager.getSubtitleText() : null;

            int fadeIn = titleEnabled ? configManager.getTitleFadeIn() : configManager.getSubtitleFadeIn();
            int stay = titleEnabled ? configManager.getTitleStay() : configManager.getSubtitleStay();
            int fadeOut = titleEnabled ? configManager.getTitleFadeOut() : configManager.getSubtitleFadeOut();

            MessageUtils.sendTitle(player, titleText, subtitleText, fadeIn, stay, fadeOut);
        }
    }

    /**
     * 检查玩家是否在冷却时间内
     */
    private boolean isOnCooldown(ProxiedPlayer player) {
        if (configManager.getCooldown() <= 0) {
            return false;
        }

        UUID playerId = player.getUniqueId();
        if (!cooldowns.containsKey(playerId)) {
            return false;
        }

        long lastUsed = cooldowns.get(playerId);
        long cooldownTime = configManager.getCooldown() * 1000L; // 转换为毫秒
        return System.currentTimeMillis() - lastUsed < cooldownTime;
    }

    /**
     * 获取剩余冷却时间（秒）
     */
    private long getRemainingCooldown(ProxiedPlayer player) {
        UUID playerId = player.getUniqueId();
        long lastUsed = cooldowns.get(playerId);
        long cooldownTime = configManager.getCooldown() * 1000L;
        long remaining = cooldownTime - (System.currentTimeMillis() - lastUsed);
        return Math.max(0, remaining / 1000);
    }

    /**
     * 设置玩家冷却时间
     */
    private void setCooldown(ProxiedPlayer player) {
        if (configManager.getCooldown() > 0) {
            cooldowns.put(player.getUniqueId(), System.currentTimeMillis());
        }
    }

    /**
     * 清理离线玩家的冷却时间数据
     */
    public void cleanupCooldowns() {
        cooldowns.entrySet().removeIf(entry -> {
            long lastUsed = entry.getValue();
            long cooldownTime = configManager.getCooldown() * 1000L;
            return System.currentTimeMillis() - lastUsed > cooldownTime;
        });
    }
}
