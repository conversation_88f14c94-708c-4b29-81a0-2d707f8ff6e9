package dev.pika.betterHUB.commands;

import dev.pika.betterHUB.BetterHUB;
import dev.pika.betterHUB.utils.MessageUtils;
import net.md_5.bungee.api.CommandSender;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.api.plugin.Command;

public class BetterHubAdminCommand extends Command {
    private final BetterHUB plugin;

    public BetterHubAdminCommand(BetterHUB plugin) {
        super("betterhub", "betterhub.admin", "bhub", "bh");
        this.plugin = plugin;
    }

    @Override
    public void execute(CommandSender sender, String[] args) {
        if (args.length == 0) {
            sendHelp(sender);
            return;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "reload":
                handleReload(sender);
                break;
            case "info":
                handleInfo(sender);
                break;
            case "help":
                sendHelp(sender);
                break;
            default:
                sendMessage(sender, "&c未知的子命令: " + subCommand, true);
                sendMessage(sender, "&e使用 /betterhub help 查看帮助", true);
                break;
        }
    }

    private void handleReload(CommandSender sender) {
        if (!sender.hasPermission("betterhub.admin.reload")) {
            sendMessage(sender, "&c你没有权限执行此命令！", true);
            return;
        }

        try {
            plugin.reloadConfig();
            sendMessage(sender, "&a配置文件已重载！", true);
        } catch (Exception e) {
            sendMessage(sender, "&c重载配置时发生错误: " + e.getMessage(), true);
            plugin.getLogger().severe("重载配置时发生错误: " + e.getMessage());
        }
    }

    private void handleInfo(CommandSender sender) {
        if (!sender.hasPermission("betterhub.admin.info")) {
            sendMessage(sender, "&c你没有权限执行此命令！", true);
            return;
        }

        sendMessage(sender, "&a&l=== BetterHUB 信息 ===", false);
        sendMessage(sender, "&e插件版本: &70.1", false);
        sendMessage(sender, "&e配置版本: &7" + plugin.getConfigManager().getConfigVersion(), false);
        sendMessage(sender, "&e大厅服务器: &7" + plugin.getConfigManager().getHubServerName(), false);
        sendMessage(sender, "&e消息前缀: &7" + (plugin.getConfigManager().isPrefixEnabled() ? "启用" : "禁用"), false);
        sendMessage(sender, "&e自定义指令: &7" + (plugin.getConfigManager().isCommandsEnabled() ? "启用" : "禁用"), false);
        sendMessage(sender, "&e指令别名: &7" + String.join(", ", plugin.getConfigManager().getCommandAliases()), false);
        sendMessage(sender, "&e传送冷却: &7" + plugin.getConfigManager().getCooldown() + " 秒", false);
        sendMessage(sender, "&eTitle消息: &7" + (plugin.getConfigManager().isTitleEnabled() ? "启用" : "禁用"), false);
        sendMessage(sender, "&eSubtitle消息: &7" + (plugin.getConfigManager().isSubtitleEnabled() ? "启用" : "禁用"), false);
        sendMessage(sender, "&e聊天栏消息: &7" + (plugin.getConfigManager().isChatEnabled() ? "启用" : "禁用"), false);
        sendMessage(sender, "&e调试模式: &7" + (plugin.getConfigManager().isDebugEnabled() ? "启用" : "禁用"), false);
    }

    private void sendHelp(CommandSender sender) {
        sendMessage(sender, "&a&l=== BetterHUB 帮助 ===", false);
        sendMessage(sender, "&e/betterhub reload &7- 重载配置文件", false);
        sendMessage(sender, "&e/betterhub info &7- 显示插件信息", false);
        sendMessage(sender, "&e/betterhub help &7- 显示此帮助信息", false);
        sendMessage(sender, "&7", false);
        sendMessage(sender, "&7别名: /bhub, /bh", false);
    }

    private void sendMessage(CommandSender sender, String message, boolean usePrefix) {
        if (sender instanceof ProxiedPlayer) {
            ProxiedPlayer player = (ProxiedPlayer) sender;
            if (usePrefix) {
                MessageUtils.sendChatMessageWithPrefix(player, message, plugin.getConfigManager());
            } else {
                MessageUtils.sendChatMessage(player, message);
            }
        } else {
            String finalMessage = message;
            if (usePrefix && plugin.getConfigManager().isPrefixEnabled()) {
                finalMessage = plugin.getConfigManager().getPrefix() + message;
            }
            sender.sendMessage(MessageUtils.colorize(finalMessage));
        }
    }
}
