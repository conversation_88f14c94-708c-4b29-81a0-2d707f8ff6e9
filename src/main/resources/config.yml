# BetterHUB 配置文件
# 配置版本 - 请勿手动修改此值
config-version: 1

# 大厅服务器设置
hub:
  # 主大厅服务器名称（必须与BungeeCord配置中的服务器名称一致）
  server-name: "lobby"

  # 分层传送设置 - 配置每个服务器对应的上级大厅
  # 格式: "当前服务器名": "目标大厅服务器名"
  # 如果某个服务器没有在此列表中，将默认传送到主大厅
  #
  # 工作原理：
  # 1. 玩家在"子服3"使用/hub → 传送到"子服2"
  # 2. 玩家在"子服2"使用/hub → 传送到"lobby"（主大厅）
  # 3. 玩家在"lobby"使用/hub → 显示"你已经回到大厅了！"
  # 4. 玩家在未配置的服务器使用/hub → 直接传送到"lobby"（主大厅）
  server-hierarchy:
    # 示例配置（请根据你的实际服务器名称修改）：
    "子服3": "子服2"                    # 单人空岛战争 → 空岛战争大厅
    "单人空岛战争": "空岛战争大厅"        # 单人空岛战争 → 空岛战争大厅
    "空岛战争大厅": "lobby"             # 空岛战争大厅 → 主大厅
    # "其他子服": "其他大厅"             # 可以添加更多配置

    # 注意：服务器名称必须与BungeeCord配置中的完全一致（区分大小写）
  
# 自定义指令设置
commands:
  # 是否启用自定义指令
  enabled: true
  # 自定义指令列表（不包含/）
  aliases:
    - "大厅"
    - "hub"
    - "lobby"
    - "spawn"

# 消息设置
messages:
  # 消息前缀设置
  prefix:
    enabled: true
    text: "&8[&aBetterHUB&8]&7 "

  # Title消息
  title:
    enabled: true
    text: "&a&l欢迎回到大厅"
    # 显示时间设置（tick为单位，20tick=1秒）
    fade-in: 10
    stay: 60
    fade-out: 10
  
  # Subtitle消息
  subtitle:
    enabled: true
    text: "&7享受你的游戏时光！"
    # 显示时间设置（tick为单位，20tick=1秒）
    fade-in: 10
    stay: 60
    fade-out: 10
  
  # 聊天栏消息
  chat:
    enabled: true
    text: "你已成功传送到大厅！"

  # 已在大厅消息
  already-in-hub: "&a你已经回到大厅了！"

  # 分层传送消息
  hierarchy-teleport: "&a你已成功传送到上级大厅！"
  hierarchy-title: "&a&l返回上级大厅"
  hierarchy-subtitle: "&7欢迎回来！"

  # 错误消息
  error:
    server-not-found: "&c错误：找不到目标服务器！"
    teleport-failed: "&c传送失败，请稍后再试！"
    rejoining-hub: "&e正在重新加入大厅..."

# 其他设置
settings:
  # 是否检查玩家是否已在大厅服务器
  check-current-server: true
  # 传送冷却时间（秒）
  cooldown: 3
  # 是否在控制台显示调试信息
  debug: false

# 高级设置
advanced:
  # 是否记录传送日志
  log-teleports: false
  # 最大传送距离限制（-1为无限制）
  max-distance: -1
