# BetterHUB 配置文件
# 配置版本 - 请勿手动修改此值
config-version: 1

# 大厅服务器设置
hub:
  # 大厅服务器名称（必须与BungeeCord配置中的服务器名称一致）
  server-name: "lobby"
  
# 自定义指令设置
commands:
  # 是否启用自定义指令
  enabled: true
  # 自定义指令列表（不包含/）
  aliases:
    - "大厅"
    - "hub"
    - "lobby"
    - "spawn"

# 消息设置
messages:
  # 消息前缀设置
  prefix:
    enabled: true
    text: "&8[&aBetterHUB&8]&7 "

  # Title消息
  title:
    enabled: true
    text: "&a&l欢迎回到大厅"
    # 显示时间设置（tick为单位，20tick=1秒）
    fade-in: 10
    stay: 60
    fade-out: 10
  
  # Subtitle消息
  subtitle:
    enabled: true
    text: "&7享受你的游戏时光！"
    # 显示时间设置（tick为单位，20tick=1秒）
    fade-in: 10
    stay: 60
    fade-out: 10
  
  # 聊天栏消息
  chat:
    enabled: true
    text: "你已成功传送到大厅！"

  # 已在大厅消息
  already-in-hub: "&a你已经回到大厅了！"

  # 错误消息
  error:
    server-not-found: "&c错误：找不到大厅服务器！"
    teleport-failed: "&c传送失败，请稍后再试！"
    rejoining-hub: "&e正在重新加入大厅..."

# 其他设置
settings:
  # 是否检查玩家是否已在大厅服务器
  check-current-server: true
  # 传送冷却时间（秒）
  cooldown: 3
  # 是否在控制台显示调试信息
  debug: false

# 高级设置
advanced:
  # 是否记录传送日志
  log-teleports: false
  # 最大传送距离限制（-1为无限制）
  max-distance: -1
