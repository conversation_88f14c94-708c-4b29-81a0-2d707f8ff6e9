name: SpawnTP
version: '0.1'
main: dev.pika.spawnTP.SpawnTP
api-version: '1.21'

commands:
  spawntp:
    description: SpawnTP插件主命令
    usage: /spawntp [setspawn|reload|toggle]
    aliases: [stp]
    permission: spawntp.admin.*
    permission-message: "&c你没有权限执行此命令！"
  spawn:
    description: 传送到出生点
    usage: /spawn
    
permissions:
  spawntp.bypass:
    description: 拥有此权限的玩家不会被自动传送到出生点
    default: false
  spawntp.bypass.cooldown:
    description: 拥有此权限的玩家可以绕过/spawn命令的冷却时间
    default: op
  spawntp.bypass.delay:
    description: 拥有此权限的玩家可以绕过/spawn命令的延迟时间
    default: op
  spawntp.admin.setspawn:
    description: 允许设置出生点位置
    default: op
  spawntp.admin.reload:
    description: 允许重载插件配置
    default: op
  spawntp.admin.toggle:
    description: 允许开启/关闭自动传送功能
    default: op
  spawntp.admin.*:
    description: 所有管理员权限
    default: op
    children:
      spawntp.admin.setspawn: true
      spawntp.admin.reload: true
      spawntp.admin.toggle: true
      spawntp.bypass.cooldown: true
      spawntp.bypass.delay: true
