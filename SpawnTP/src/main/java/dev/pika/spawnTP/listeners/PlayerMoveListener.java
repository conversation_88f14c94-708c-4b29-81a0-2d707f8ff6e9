package dev.pika.spawnTP.listeners;

import dev.pika.spawnTP.SpawnTP;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;

public class PlayerMoveListener implements Listener {
    
    private final SpawnTP plugin;
    
    public PlayerMoveListener(SpawnTP plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerMove(PlayerMoveEvent event) {
        // 只有当配置启用了移动取消传送时才检查
        if (!plugin.getConfigManager().isCancelOnMove()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // 检查玩家是否有待处理的传送任务
        if (!plugin.getTeleportManager().hasPendingTeleport(player)) {
            return;
        }
        
        // 检查玩家是否已经移动
        if (plugin.getTeleportManager().hasPlayerMoved(player)) {
            // 取消传送
            plugin.getTeleportManager().cancelTeleportWithReason(player, "spawn-cancelled");
        }
    }
} 