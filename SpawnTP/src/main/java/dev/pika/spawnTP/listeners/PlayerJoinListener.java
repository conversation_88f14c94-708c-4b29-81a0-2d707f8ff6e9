package dev.pika.spawnTP.listeners;

import dev.pika.spawnTP.SpawnTP;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.scheduler.BukkitRunnable;

public class PlayerJoinListener implements Listener {
    
    private final SpawnTP plugin;
    
    public PlayerJoinListener(SpawnTP plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // 检查插件是否启用
        if (!plugin.getConfigManager().isEnabled()) {
            return;
        }
        
        // 检查玩家是否有绕过权限
        if (plugin.getConfigManager().hasPermissionBypass(player)) {
            return;
        }
        
        // 检查玩家所在世界是否启用
        if (!plugin.getConfigManager().isWorldEnabled(player.getWorld())) {
            return;
        }
        
        // 获取传送位置
        Location spawnLocation = plugin.getConfigManager().getSpawnLocation();
        if (spawnLocation == null) {
            plugin.getLogger().warning("无法获取传送位置，请检查配置！");
            return;
        }
        
        // 延迟一tick传送玩家以确保客户端加载完毕
        new BukkitRunnable() {
            @Override
            public void run() {
                player.teleport(spawnLocation);
                
                // 根据配置决定是否显示传送消息
                if (plugin.getConfigManager().showTeleportMessage() && plugin.getConfigManager().showJoinMessage()) {
                    player.sendMessage(plugin.getConfigManager().getMessage("teleported"));
                }
            }
        }.runTaskLater(plugin, 1L);
    }
} 