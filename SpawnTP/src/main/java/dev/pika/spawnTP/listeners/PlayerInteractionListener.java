package dev.pika.spawnTP.listeners;

import dev.pika.spawnTP.SpawnTP;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerInteractEvent;

public class PlayerInteractionListener implements Listener {
    
    private final SpawnTP plugin;
    
    public PlayerInteractionListener(SpawnTP plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerAttack(EntityDamageByEntityEvent event) {
        // 只有当配置启用了攻击取消传送时才检查
        if (!plugin.getConfigManager().isCancelOnAttack()) {
            return;
        }
        
        // 检查是否是玩家在攻击
        if (!(event.getDamager() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getDamager();
        
        // 检查玩家是否有待处理的传送任务
        if (plugin.getTeleportManager().hasPendingTeleport(player)) {
            // 取消传送
            plugin.getTeleportManager().cancelTeleportWithReason(player, "spawn-cancelled-attack");
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onBlockPlace(BlockPlaceEvent event) {
        // 只有当配置启用了放置方块取消传送时才检查
        if (!plugin.getConfigManager().isCancelOnBlockPlace()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // 检查玩家是否有待处理的传送任务
        if (plugin.getTeleportManager().hasPendingTeleport(player)) {
            // 取消传送
            plugin.getTeleportManager().cancelTeleportWithReason(player, "spawn-cancelled-block-place");
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onBlockBreak(BlockBreakEvent event) {
        // 只有当配置启用了破坏方块取消传送时才检查
        if (!plugin.getConfigManager().isCancelOnBlockBreak()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // 检查玩家是否有待处理的传送任务
        if (plugin.getTeleportManager().hasPendingTeleport(player)) {
            // 取消传送
            plugin.getTeleportManager().cancelTeleportWithReason(player, "spawn-cancelled-block-break");
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerDamaged(EntityDamageEvent event) {
        // 只有当配置启用了受到伤害取消传送时才检查
        if (!plugin.getConfigManager().isCancelOnDamage()) {
            return;
        }
        
        // 检查是否是玩家受到伤害
        if (!(event.getEntity() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getEntity();
        
        // 检查玩家是否有待处理的传送任务
        if (plugin.getTeleportManager().hasPendingTeleport(player)) {
            // 取消传送
            plugin.getTeleportManager().cancelTeleportWithReason(player, "spawn-cancelled-damage");
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerInteract(PlayerInteractEvent event) {
        // 只有当配置启用了交互取消传送时才检查
        if (!plugin.getConfigManager().isCancelOnInteract()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // 检查玩家是否有待处理的传送任务
        if (plugin.getTeleportManager().hasPendingTeleport(player)) {
            // 取消传送
            plugin.getTeleportManager().cancelTeleportWithReason(player, "spawn-cancelled-interact");
        }
    }
} 