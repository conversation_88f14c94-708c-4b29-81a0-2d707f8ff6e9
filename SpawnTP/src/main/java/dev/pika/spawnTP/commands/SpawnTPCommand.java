package dev.pika.spawnTP.commands;

import dev.pika.spawnTP.SpawnTP;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class SpawnTPCommand implements CommandExecutor, TabCompleter {
    
    private final SpawnTP plugin;
    private final List<String> subcommands = Arrays.asList("setspawn", "reload", "toggle");
    
    public SpawnTPCommand(SpawnTP plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 检查是否有管理员权限
        boolean hasAdminPermission = sender.hasPermission("spawntp.admin.setspawn") || 
                                    sender.hasPermission("spawntp.admin.reload") || 
                                    sender.hasPermission("spawntp.admin.toggle") ||
                                    sender.hasPermission("spawntp.admin.*");
        
        if (args.length == 0) {
            // 如果没有管理员权限，显示无权限消息
            if (!hasAdminPermission) {
                sender.sendMessage(plugin.getConfigManager().getMessage("no-permission"));
                return true;
            }
            
            sender.sendMessage(plugin.colorize("&8[&bSpawnTP&8] &7命令用法:"));
            sender.sendMessage(plugin.colorize("&b/spawntp setspawn &7- 设置当前位置为传送点"));
            sender.sendMessage(plugin.colorize("&b/spawntp reload &7- 重新加载配置文件"));
            sender.sendMessage(plugin.colorize("&b/spawntp toggle &7- 切换是否启用自动传送"));
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        if (subCommand.equals("setspawn")) {
            if (!(sender instanceof Player)) {
                sender.sendMessage(plugin.colorize("&c此命令只能由玩家执行！"));
                return true;
            }
            
            Player player = (Player) sender;
            if (!player.hasPermission("spawntp.admin.setspawn")) {
                player.sendMessage(plugin.getConfigManager().getMessage("no-permission"));
                return true;
            }
            
            plugin.getConfigManager().setSpawnLocation(player.getLocation());
            player.sendMessage(plugin.getConfigManager().getMessage("spawn-set"));
            return true;
        }
        
        if (subCommand.equals("reload")) {
            if (!sender.hasPermission("spawntp.admin.reload")) {
                sender.sendMessage(plugin.getConfigManager().getMessage("no-permission"));
                return true;
            }
            
            plugin.reloadConfig();
            sender.sendMessage(plugin.getConfigManager().getMessage("config-reloaded"));
            return true;
        }
        
        if (subCommand.equals("toggle")) {
            if (!sender.hasPermission("spawntp.admin.toggle")) {
                sender.sendMessage(plugin.getConfigManager().getMessage("no-permission"));
                return true;
            }
            
            boolean isEnabled = plugin.getConfigManager().isEnabled();
            plugin.getConfig().set("spawn.enabled", !isEnabled);
            plugin.saveConfig();
            
            if (isEnabled) {
                sender.sendMessage(plugin.getConfigManager().getMessage("plugin-disabled"));
            } else {
                sender.sendMessage(plugin.getConfigManager().getMessage("plugin-enabled"));
            }
            return true;
        }
        
        // 如果输入了未知的子命令，并且有管理员权限，才显示提示信息
        if (hasAdminPermission) {
            sender.sendMessage(plugin.colorize("&c未知的子命令，请使用 /spawntp 查看可用命令。"));
        } else {
            sender.sendMessage(plugin.getConfigManager().getMessage("no-permission"));
        }
        return true;
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        // 只有有管理员权限的玩家才能看到Tab补全
        boolean hasAdminPermission = sender.hasPermission("spawntp.admin.setspawn") || 
                                   sender.hasPermission("spawntp.admin.reload") || 
                                   sender.hasPermission("spawntp.admin.toggle") ||
                                   sender.hasPermission("spawntp.admin.*");
                                   
        if (!hasAdminPermission) {
            return new ArrayList<>();
        }
        
        if (args.length == 1) {
            return subcommands.stream()
                    .filter(s -> s.startsWith(args[0].toLowerCase()))
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }
} 