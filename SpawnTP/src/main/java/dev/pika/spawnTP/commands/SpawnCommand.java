package dev.pika.spawnTP.commands;

import dev.pika.spawnTP.SpawnTP;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class SpawnCommand implements CommandExecutor {
    
    private final SpawnTP plugin;
    
    public SpawnCommand(SpawnTP plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 只有玩家可以使用此命令
        if (!(sender instanceof Player)) {
            sender.sendMessage(plugin.colorize("&c此命令只能由玩家执行！"));
            return true;
        }
        
        Player player = (Player) sender;
        
        // 检查命令是否启用
        if (!plugin.getConfigManager().isSpawnCommandEnabled()) {
            player.sendMessage(plugin.getConfigManager().getMessage("spawn-command-disabled"));
            return true;
        }
        
        // 获取传送位置
        Location spawnLocation = plugin.getConfigManager().getSpawnLocation();
        if (spawnLocation == null) {
            player.sendMessage(plugin.colorize("&c无法获取传送位置，请联系服务器管理员！"));
            return true;
        }
        
        // 检查冷却时间
        if (!plugin.getConfigManager().hasPermissionBypassCooldown(player) && 
            plugin.getCooldownManager().isOnCooldown(player.getUniqueId())) {
            int remaining = plugin.getCooldownManager().getRemainingCooldown(player.getUniqueId());
            player.sendMessage(plugin.getConfigManager().getMessage("spawn-cooldown", "{time}", String.valueOf(remaining)));
            return true;
        }
        
        // 获取延迟时间
        int delay = plugin.getConfigManager().hasPermissionBypassDelay(player) ? 0 : plugin.getConfigManager().getSpawnCommandDelay();
        
        // 开始传送
        plugin.getTeleportManager().teleport(player, spawnLocation, delay, plugin.getConfigManager().isCancelOnMove());
        
        // 如果有冷却时间，设置冷却
        if (!plugin.getConfigManager().hasPermissionBypassCooldown(player)) {
            plugin.getCooldownManager().setCooldown(player.getUniqueId(), plugin.getConfigManager().getSpawnCommandCooldown());
        }
        
        return true;
    }
} 