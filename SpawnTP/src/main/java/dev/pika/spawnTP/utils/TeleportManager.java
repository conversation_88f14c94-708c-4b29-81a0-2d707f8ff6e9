package dev.pika.spawnTP.utils;

import dev.pika.spawnTP.SpawnTP;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class TeleportManager {
    
    private final SpawnTP plugin;
    private final Map<UUID, BukkitTask> pendingTeleports = new HashMap<>();
    private final Map<UUID, Location> playerLocations = new HashMap<>();
    
    public TeleportManager(SpawnTP plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 开始一个延迟传送任务
     * 
     * @param player 要传送的玩家
     * @param location 目标位置
     * @param delay 延迟时间（秒）
     * @param cancelOnMove 是否在移动时取消传送
     */
    public void teleport(Player player, Location location, int delay, boolean cancelOnMove) {
        UUID uuid = player.getUniqueId();
        
        // 如果已经有一个传送任务，取消它
        cancelTeleport(player);
        
        // 如果延迟为0，立即传送
        if (delay <= 0) {
            player.teleport(location);
            
            // 播放成功音效
            plugin.getTitleManager().playSuccessSound(player);
            
            // 显示成功标题
            plugin.getTitleManager().sendSuccessTitle(player);
            
            // 显示传送成功消息
            if (plugin.getConfigManager().showSpawnCommandMessage()) {
                player.sendMessage(plugin.getConfigManager().getMessage("spawn-teleported"));
            }
            return;
        }
        
        // 保存玩家当前位置（用于检测移动）
        if (cancelOnMove) {
            playerLocations.put(uuid, player.getLocation().clone());
        }
        
        // 显示传送消息
        if (plugin.getConfigManager().showSpawnCommandMessage()) {
            player.sendMessage(plugin.getConfigManager().getMessage("spawn-teleporting", "{delay}", String.valueOf(delay)));
        }
        
        // 播放开始传送音效
        plugin.getTitleManager().playStartSound(player);
        
        // 创建倒计时任务
        final int[] countdown = {delay};
        final int totalTime = delay;
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                // 如果玩家已经离线，取消传送
                if (!player.isOnline()) {
                    cancel();
                    pendingTeleports.remove(uuid);
                    playerLocations.remove(uuid);
                    return;
                }
                
                // 显示倒计时标题（使用颜色渐变）
                plugin.getTitleManager().sendCountdownTitle(player, countdown[0], totalTime);
                
                // 如果倒计时结束，执行传送
                if (countdown[0] <= 0) {
                    cancel();
                    pendingTeleports.remove(uuid);
                    playerLocations.remove(uuid);
                    
                    // 清除标题
                    plugin.getTitleManager().clearTitle(player);
                    
                    // 传送玩家
                    player.teleport(location);
                    
                    // 播放成功音效
                    plugin.getTitleManager().playSuccessSound(player);
                    
                    // 显示成功标题
                    plugin.getTitleManager().sendSuccessTitle(player);
                    
                    // 显示传送成功消息
                    if (plugin.getConfigManager().showSpawnCommandMessage()) {
                        player.sendMessage(plugin.getConfigManager().getMessage("spawn-teleported"));
                    }
                    return;
                }
                
                // 播放倒计时音效（使用音调变化）
                plugin.getTitleManager().playCountdownSound(player, countdown[0], totalTime);
                
                // 减少倒计时
                countdown[0]--;
            }
        }.runTaskTimer(plugin, 0L, 20L); // 每秒执行一次
        
        pendingTeleports.put(uuid, task);
    }
    
    /**
     * 取消玩家的传送任务
     * 
     * @param player 玩家
     */
    public void cancelTeleport(Player player) {
        UUID uuid = player.getUniqueId();
        
        if (pendingTeleports.containsKey(uuid)) {
            pendingTeleports.get(uuid).cancel();
            pendingTeleports.remove(uuid);
            playerLocations.remove(uuid);
            
            // 清除标题
            plugin.getTitleManager().clearTitle(player);
        }
    }
    
    /**
     * 取消玩家的传送任务并显示取消原因
     * 
     * @param player 玩家
     * @param cancelReason 取消原因的消息键
     */
    public void cancelTeleportWithReason(Player player, String cancelReason) {
        if (hasPendingTeleport(player)) {
            // 取消传送
            cancelTeleport(player);
            
            // 播放取消音效
            plugin.getTitleManager().playCancelSound(player);
            
            // 显示取消标题
            String reasonKey = cancelReason.replace("spawn-cancelled-", "").replace("spawn-cancelled", "move");
            if (reasonKey.isEmpty()) {
                reasonKey = "move";
            }
            plugin.getTitleManager().sendCancelTitle(player, reasonKey);
            
            // 显示取消消息
            if (plugin.getConfigManager().showSpawnCommandMessage()) {
                player.sendMessage(plugin.getConfigManager().getMessage(cancelReason));
            }
        }
    }
    
    /**
     * 检查玩家是否有待处理的传送任务
     * 
     * @param player 玩家
     * @return 是否有待处理的传送任务
     */
    public boolean hasPendingTeleport(Player player) {
        return pendingTeleports.containsKey(player.getUniqueId());
    }
    
    /**
     * 检查玩家是否已经移动（用于取消传送）
     * 
     * @param player 玩家
     * @return 是否已移动
     */
    public boolean hasPlayerMoved(Player player) {
        UUID uuid = player.getUniqueId();
        
        if (!playerLocations.containsKey(uuid)) {
            return false;
        }
        
        Location savedLoc = playerLocations.get(uuid);
        Location currentLoc = player.getLocation();
        
        // 检查玩家是否移动了（只检查xyz坐标，不检查朝向）
        return savedLoc.getWorld() != currentLoc.getWorld() ||
               savedLoc.getX() != currentLoc.getX() ||
               savedLoc.getY() != currentLoc.getY() ||
               savedLoc.getZ() != currentLoc.getZ();
    }
} 