package dev.pika.spawnTP.utils;

import dev.pika.spawnTP.SpawnTP;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;

import java.util.List;

public class ConfigManager {
    
    private final SpawnTP plugin;
    
    public ConfigManager(SpawnTP plugin) {
        this.plugin = plugin;
    }
    
    public boolean isEnabled() {
        return plugin.getConfig().getBoolean("spawn.enabled", true);
    }
    
    public boolean showTeleportMessage() {
        return plugin.getConfig().getBoolean("spawn.show-teleport-message", true);
    }
    
    public boolean showJoinMessage() {
        return plugin.getConfig().getBoolean("spawn.show-join-message", false);
    }
    
    // Spawn命令相关配置
    public boolean isSpawnCommandEnabled() {
        return plugin.getConfig().getBoolean("spawn.command.enabled", true);
    }
    
    public int getSpawnCommandDelay() {
        return plugin.getConfig().getInt("spawn.command.delay", 3);
    }
    
    public int getSpawnCommandCooldown() {
        return plugin.getConfig().getInt("spawn.command.cooldown", 30);
    }
    
    public boolean showSpawnCommandMessage() {
        return plugin.getConfig().getBoolean("spawn.command.show-message", true);
    }
    
    public boolean isCancelOnMove() {
        return plugin.getConfig().getBoolean("spawn.command.cancel-on-move", true);
    }
    
    // 取消传送的条件
    public boolean isCancelOnAttack() {
        return plugin.getConfig().getBoolean("spawn.command.cancel-on.attack", true);
    }
    
    public boolean isCancelOnBlockPlace() {
        return plugin.getConfig().getBoolean("spawn.command.cancel-on.block-place", true);
    }
    
    public boolean isCancelOnBlockBreak() {
        return plugin.getConfig().getBoolean("spawn.command.cancel-on.block-break", true);
    }
    
    public boolean isCancelOnDamage() {
        return plugin.getConfig().getBoolean("spawn.command.cancel-on.damage", true);
    }
    
    public boolean isCancelOnInteract() {
        return plugin.getConfig().getBoolean("spawn.command.cancel-on.interact", true);
    }
    
    // 标题提示配置
    public boolean isTitleEnabled() {
        return plugin.getConfig().getBoolean("spawn.command.title.enabled", true);
    }
    
    public boolean isShowTitle() {
        return plugin.getConfig().getBoolean("spawn.command.title.show-title", true);
    }
    
    public boolean isShowSubtitle() {
        return plugin.getConfig().getBoolean("spawn.command.title.show-subtitle", true);
    }
    
    public String getTitleText() {
        return plugin.getConfig().getString("spawn.command.title.text", "&b传送中...");
    }
    
    public String getSubtitleText() {
        return plugin.getConfig().getString("spawn.command.title.subtitle", "&e请等待 &c{time} &e秒");
    }
    
    public int getTitleFadeIn() {
        return plugin.getConfig().getInt("spawn.command.title.fade-in", 5);
    }
    
    public int getTitleStay() {
        return plugin.getConfig().getInt("spawn.command.title.stay", 20);
    }
    
    public int getTitleFadeOut() {
        return plugin.getConfig().getInt("spawn.command.title.fade-out", 5);
    }
    
    // 取消标题配置
    public boolean isCancelTitleEnabled() {
        return isTitleEnabled() && plugin.getConfig().getBoolean("spawn.command.title.cancel.enabled", true);
    }
    
    public boolean isShowCancelTitle() {
        return plugin.getConfig().getBoolean("spawn.command.title.cancel.show-title", true);
    }
    
    public boolean isShowCancelSubtitle() {
        return plugin.getConfig().getBoolean("spawn.command.title.cancel.show-subtitle", true);
    }
    
    public String getCancelTitleText() {
        return plugin.getConfig().getString("spawn.command.title.cancel.text", "&c传送已取消!");
    }
    
    public String getCancelSubtitleText() {
        return plugin.getConfig().getString("spawn.command.title.cancel.subtitle", "&7原因: &f{reason}");
    }
    
    public int getCancelTitleFadeIn() {
        return plugin.getConfig().getInt("spawn.command.title.cancel.fade-in", 5);
    }
    
    public int getCancelTitleStay() {
        return plugin.getConfig().getInt("spawn.command.title.cancel.stay", 40);
    }
    
    public int getCancelTitleFadeOut() {
        return plugin.getConfig().getInt("spawn.command.title.cancel.fade-out", 5);
    }
    
    // 获取取消原因文本
    public String getCancelReasonText(String reason) {
        return plugin.getConfig().getString("messages.reason-" + reason, reason);
    }
    
    // 音效配置
    public boolean isSoundsEnabled() {
        return plugin.getConfig().getBoolean("spawn.command.sounds.enabled", true);
    }
    
    public boolean isStartSoundEnabled() {
        return isSoundsEnabled() && plugin.getConfig().getBoolean("spawn.command.sounds.start.enabled", true);
    }
    
    public String getStartSoundName() {
        return plugin.getConfig().getString("spawn.command.sounds.start.sound", "entity.enderman.teleport");
    }
    
    public float getStartSoundVolume() {
        return (float) plugin.getConfig().getDouble("spawn.command.sounds.start.volume", 1.0);
    }
    
    public float getStartSoundPitch() {
        return (float) plugin.getConfig().getDouble("spawn.command.sounds.start.pitch", 0.5);
    }
    
    public boolean isCountdownSoundEnabled() {
        return isSoundsEnabled() && plugin.getConfig().getBoolean("spawn.command.sounds.countdown.enabled", true);
    }
    
    public String getCountdownSoundName() {
        return plugin.getConfig().getString("spawn.command.sounds.countdown.sound", "block.note_block.pling");
    }
    
    public float getCountdownSoundVolume() {
        return (float) plugin.getConfig().getDouble("spawn.command.sounds.countdown.volume", 1.0);
    }
    
    public float getCountdownSoundPitch() {
        return (float) plugin.getConfig().getDouble("spawn.command.sounds.countdown.pitch", 1.0);
    }
    
    public boolean isSuccessSoundEnabled() {
        return isSoundsEnabled() && plugin.getConfig().getBoolean("spawn.command.sounds.success.enabled", true);
    }
    
    public String getSuccessSoundName() {
        return plugin.getConfig().getString("spawn.command.sounds.success.sound", "entity.player.levelup");
    }
    
    public float getSuccessSoundVolume() {
        return (float) plugin.getConfig().getDouble("spawn.command.sounds.success.volume", 1.0);
    }
    
    public float getSuccessSoundPitch() {
        return (float) plugin.getConfig().getDouble("spawn.command.sounds.success.pitch", 1.0);
    }
    
    public boolean isCancelSoundEnabled() {
        return isSoundsEnabled() && plugin.getConfig().getBoolean("spawn.command.sounds.cancel.enabled", true);
    }
    
    public String getCancelSoundName() {
        return plugin.getConfig().getString("spawn.command.sounds.cancel.sound", "entity.villager.no");
    }
    
    public float getCancelSoundVolume() {
        return (float) plugin.getConfig().getDouble("spawn.command.sounds.cancel.volume", 1.0);
    }
    
    public float getCancelSoundPitch() {
        return (float) plugin.getConfig().getDouble("spawn.command.sounds.cancel.pitch", 1.0);
    }
    
    public boolean hasPermissionBypassCooldown(Player player) {
        String bypass = plugin.getConfig().getString("permissions.bypass-cooldown", "spawntp.bypass.cooldown");
        return player.hasPermission(bypass);
    }
    
    public boolean hasPermissionBypassDelay(Player player) {
        String bypass = plugin.getConfig().getString("permissions.bypass-delay", "spawntp.bypass.delay");
        return player.hasPermission(bypass);
    }
    
    public Location getSpawnLocation() {
        ConfigurationSection section = plugin.getConfig().getConfigurationSection("spawn.location");
        if (section == null) {
            return null;
        }
        
        String worldName = section.getString("world", "world");
        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            plugin.getLogger().warning("配置中指定的世界 '" + worldName + "' 不存在！");
            return null;
        }
        
        double x = section.getDouble("x", 0.0);
        double y = section.getDouble("y", 64.0);
        double z = section.getDouble("z", 0.0);
        float yaw = (float) section.getDouble("yaw", 0.0);
        float pitch = (float) section.getDouble("pitch", 0.0);
        
        return new Location(world, x, y, z, yaw, pitch);
    }
    
    public void setSpawnLocation(Location location) {
        ConfigurationSection section = plugin.getConfig().getConfigurationSection("spawn.location");
        if (section == null) {
            section = plugin.getConfig().createSection("spawn.location");
        }
        
        section.set("world", location.getWorld().getName());
        section.set("x", location.getX());
        section.set("y", location.getY());
        section.set("z", location.getZ());
        section.set("yaw", location.getYaw());
        section.set("pitch", location.getPitch());
        
        plugin.saveConfig();
    }
    
    public boolean hasPermissionBypass(Player player) {
        String bypass = plugin.getConfig().getString("permissions.bypass", "spawntp.bypass");
        return player.hasPermission(bypass);
    }
    
    public boolean isSpecificWorldOnly() {
        return plugin.getConfig().getBoolean("worlds.specific-world-only", false);
    }
    
    public List<String> getEnabledWorlds() {
        return plugin.getConfig().getStringList("worlds.enabled-worlds");
    }
    
    public boolean isWorldEnabled(World world) {
        if (!isSpecificWorldOnly()) {
            return true;
        }
        return getEnabledWorlds().contains(world.getName());
    }
    
    public String getMessage(String path) {
        String prefix = plugin.getConfig().getString("messages.prefix", "&8[&bSpawnTP&8] &7");
        String message = plugin.getConfig().getString("messages." + path, "");
        return plugin.colorize(prefix + message);
    }
    
    public String getMessage(String path, String... replacements) {
        String message = getMessage(path);
        
        if (replacements != null && replacements.length % 2 == 0) {
            for (int i = 0; i < replacements.length; i += 2) {
                message = message.replace(replacements[i], replacements[i + 1]);
            }
        }
        
        return message;
    }
    
    // 倒计时颜色渐变配置
    public boolean isCountdownColorEnabled() {
        return plugin.getConfig().getBoolean("spawn.command.title.countdown-color.enabled", true);
    }
    
    public String getCountdownStartColor() {
        return plugin.getConfig().getString("spawn.command.title.countdown-color.start-color", "&b");
    }
    
    public String getCountdownEndColor() {
        return plugin.getConfig().getString("spawn.command.title.countdown-color.end-color", "&c");
    }
    
    /**
     * 获取基于倒计时进度的颜色
     * 
     * @param currentTime 当前剩余时间
     * @param totalTime 总倒计时时间
     * @return 对应的颜色代码
     */
    public String getTimeColor(int currentTime, int totalTime) {
        if (!isCountdownColorEnabled()) {
            return "&c"; // 如果未启用颜色渐变，则使用默认颜色
        }
        
        // 如果时间为0或负数，直接返回结束颜色
        if (currentTime <= 0 || totalTime <= 0) {
            return getCountdownEndColor();
        }
        
        // 如果当前时间大于等于总时间，直接返回开始颜色
        if (currentTime >= totalTime) {
            return getCountdownStartColor();
        }
        
        // 计算进度 (0.0 - 1.0)
        float progress = (float) currentTime / totalTime;
        
        // 获取开始和结束颜色
        String startColorCode = getCountdownStartColor();
        String endColorCode = getCountdownEndColor();
        
        // 如果开始或结束颜色不是有效的颜色代码，则使用默认颜色
        if (!startColorCode.startsWith("&") || startColorCode.length() < 2) {
            startColorCode = "&b";
        }
        if (!endColorCode.startsWith("&") || endColorCode.length() < 2) {
            endColorCode = "&c";
        }
        
        // 简单的颜色渐变逻辑：根据进度选择颜色
        if (progress > 0.66) {
            return startColorCode; // 开始颜色
        } else if (progress > 0.33) {
            return "&e"; // 中间颜色（黄色）
        } else {
            return endColorCode; // 结束颜色
        }
    }
    
    // 音调变化配置
    public boolean isPitchChangeEnabled() {
        return plugin.getConfig().getBoolean("spawn.command.sounds.countdown.pitch-change.enabled", true);
    }
    
    public float getStartPitch() {
        return (float) plugin.getConfig().getDouble("spawn.command.sounds.countdown.pitch-change.start-pitch", 0.8);
    }
    
    public float getEndPitch() {
        return (float) plugin.getConfig().getDouble("spawn.command.sounds.countdown.pitch-change.end-pitch", 2.0);
    }
    
    /**
     * 获取基于倒计时进度的音调
     * 
     * @param currentTime 当前剩余时间
     * @param totalTime 总倒计时时间
     * @return 对应的音调值
     */
    public float getTimePitch(int currentTime, int totalTime) {
        if (!isPitchChangeEnabled()) {
            return getCountdownSoundPitch(); // 如果未启用音调变化，则使用基础音调
        }
        
        // 如果时间为0或负数，直接返回结束音调
        if (currentTime <= 0 || totalTime <= 0) {
            return getEndPitch();
        }
        
        // 如果当前时间大于等于总时间，直接返回开始音调
        if (currentTime >= totalTime) {
            return getStartPitch();
        }
        
        // 计算进度 (0.0 - 1.0)
        float progress = (float) currentTime / totalTime;
        
        // 线性插值计算当前音调
        float startPitch = getStartPitch();
        float endPitch = getEndPitch();
        
        // 音调随着倒计时减少而增加
        return startPitch + (endPitch - startPitch) * (1.0f - progress);
    }
    
    // 传送成功标题配置
    public boolean isSuccessTitleEnabled() {
        return isTitleEnabled() && plugin.getConfig().getBoolean("spawn.command.title.success.enabled", true);
    }
    
    public boolean isShowSuccessTitle() {
        return plugin.getConfig().getBoolean("spawn.command.title.success.show-title", true);
    }
    
    public boolean isShowSuccessSubtitle() {
        return plugin.getConfig().getBoolean("spawn.command.title.success.show-subtitle", true);
    }
    
    public String getSuccessTitleText() {
        return plugin.getConfig().getString("spawn.command.title.success.text", "&a传送成功!");
    }
    
    public String getSuccessSubtitleText() {
        return plugin.getConfig().getString("spawn.command.title.success.subtitle", "&7你已被传送到出生点");
    }
    
    public int getSuccessTitleFadeIn() {
        return plugin.getConfig().getInt("spawn.command.title.success.fade-in", 5);
    }
    
    public int getSuccessTitleStay() {
        return plugin.getConfig().getInt("spawn.command.title.success.stay", 30);
    }
    
    public int getSuccessTitleFadeOut() {
        return plugin.getConfig().getInt("spawn.command.title.success.fade-out", 5);
    }
} 