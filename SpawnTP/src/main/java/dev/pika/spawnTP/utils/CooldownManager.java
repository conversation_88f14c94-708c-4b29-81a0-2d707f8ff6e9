package dev.pika.spawnTP.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class CooldownManager {
    
    private final Map<UUID, Long> cooldowns = new HashMap<>();
    
    /**
     * 设置玩家的冷却时间
     * 
     * @param uuid 玩家UUID
     * @param seconds 冷却时间（秒）
     */
    public void setCooldown(UUID uuid, int seconds) {
        cooldowns.put(uuid, System.currentTimeMillis() + (seconds * 1000L));
    }
    
    /**
     * 检查玩家是否在冷却中
     * 
     * @param uuid 玩家UUID
     * @return 是否在冷却中
     */
    public boolean isOnCooldown(UUID uuid) {
        if (!cooldowns.containsKey(uuid)) {
            return false;
        }
        
        return cooldowns.get(uuid) > System.currentTimeMillis();
    }
    
    /**
     * 获取玩家剩余的冷却时间（秒）
     * 
     * @param uuid 玩家UUID
     * @return 剩余冷却时间（秒）
     */
    public int getRemainingCooldown(UUID uuid) {
        if (!isOnCooldown(uuid)) {
            return 0;
        }
        
        long remaining = cooldowns.get(uuid) - System.currentTimeMillis();
        return (int) Math.ceil(remaining / 1000.0);
    }
    
    /**
     * 移除玩家的冷却时间
     * 
     * @param uuid 玩家UUID
     */
    public void removeCooldown(UUID uuid) {
        cooldowns.remove(uuid);
    }
} 