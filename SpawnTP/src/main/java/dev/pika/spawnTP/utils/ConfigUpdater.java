package dev.pika.spawnTP.utils;

import dev.pika.spawnTP.SpawnTP;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.*;

public class ConfigUpdater {
    
    private final SpawnTP plugin;
    
    public ConfigUpdater(SpawnTP plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 检查并更新配置文件中缺失的配置项
     * 
     * @return 是否有更新配置
     */
    public boolean updateConfig() {
        File configFile = new File(plugin.getDataFolder(), "config.yml");
        if (!configFile.exists()) {
            plugin.getLogger().warning("配置文件不存在，无法更新!");
            return false;
        }
        
        // 创建备份文件
        File backupFile = new File(plugin.getDataFolder(), "config.yml.bak");
        try {
            Files.copy(configFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            plugin.getLogger().warning("无法创建配置文件备份: " + e.getMessage());
            // 继续执行，即使备份失败
        }
        
        // 加载当前配置
        YamlConfiguration currentConfig = YamlConfiguration.loadConfiguration(configFile);
        
        // 加载默认配置
        InputStream defaultConfigStream = plugin.getResource("config.yml");
        if (defaultConfigStream == null) {
            plugin.getLogger().warning("无法获取默认配置文件!");
            return false;
        }
        
        YamlConfiguration defaultConfig = YamlConfiguration.loadConfiguration(
            new InputStreamReader(defaultConfigStream, StandardCharsets.UTF_8)
        );
        
        // 跟踪是否有更新
        boolean updated = false;
        
        // 获取所有默认配置路径
        Set<String> defaultKeys = defaultConfig.getKeys(true);
        for (String key : defaultKeys) {
            // 如果当前配置中不存在此路径
            if (!currentConfig.contains(key)) {
                // 如果是配置节，跳过（我们会处理其子节点）
                if (defaultConfig.isConfigurationSection(key)) {
                    continue;
                }
                
                // 获取默认值并设置到当前配置
                Object defaultValue = defaultConfig.get(key);
                currentConfig.set(key, defaultValue);
                plugin.getLogger().info("添加新配置项: " + key + " = " + defaultValue);
                updated = true;
            }
        }
        
        // 如果有更新，保存配置
        if (updated) {
            try {
                currentConfig.save(configFile);
                plugin.getLogger().info("配置文件已更新，新的配置项已添加。");
            } catch (IOException e) {
                plugin.getLogger().severe("保存更新后的配置文件时出错: " + e.getMessage());
                
                // 尝试恢复备份
                try {
                    if (backupFile.exists()) {
                        Files.copy(backupFile.toPath(), configFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                        plugin.getLogger().info("已恢复配置文件备份。");
                    }
                } catch (IOException ex) {
                    plugin.getLogger().severe("恢复配置文件备份时出错: " + ex.getMessage());
                }
                
                return false;
            }
        } else {
            plugin.getLogger().info("配置文件已是最新，无需更新。");
        }
        
        // 尝试删除备份文件
        if (backupFile.exists() && updated) {
            try {
                Files.delete(backupFile.toPath());
            } catch (IOException e) {
                // 忽略删除备份文件的错误
            }
        }
        
        return updated;
    }
    
    /**
     * 合并两个配置节，保留用户设置的值
     * 
     * @param target 目标配置节
     * @param source 源配置节
     * @param path 当前路径
     * @return 是否有更新
     */
    private boolean mergeConfigurations(ConfigurationSection target, ConfigurationSection source, String path) {
        boolean updated = false;
        
        for (String key : source.getKeys(false)) {
            String currentPath = path.isEmpty() ? key : path + "." + key;
            
            if (!target.contains(key)) {
                // 如果目标中不存在此键，直接添加
                target.set(key, source.get(key));
                plugin.getLogger().info("添加配置项: " + currentPath);
                updated = true;
            } else if (source.isConfigurationSection(key)) {
                // 如果是配置节，递归合并
                ConfigurationSection sourceSection = source.getConfigurationSection(key);
                
                if (!target.isConfigurationSection(key)) {
                    // 如果目标中此键不是配置节，覆盖它
                    target.set(key, source.get(key));
                    plugin.getLogger().info("更新配置项: " + currentPath);
                    updated = true;
                } else {
                    // 递归合并子节
                    ConfigurationSection targetSection = target.getConfigurationSection(key);
                    boolean sectionUpdated = mergeConfigurations(targetSection, sourceSection, currentPath);
                    if (sectionUpdated) {
                        updated = true;
                    }
                }
            }
            // 如果目标中已存在此键且不是配置节，保留用户设置的值
        }
        
        return updated;
    }
} 