package dev.pika.spawnTP.utils;

import dev.pika.spawnTP.SpawnTP;
import org.bukkit.entity.Player;

public class TitleManager {
    
    private final SpawnTP plugin;
    
    public TitleManager(SpawnTP plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 向玩家发送传送倒计时标题
     * 
     * @param player 玩家
     * @param time 剩余时间（秒）
     * @param totalTime 总倒计时时间（秒）
     */
    public void sendCountdownTitle(Player player, int time, int totalTime) {
        if (!plugin.getConfigManager().isTitleEnabled()) {
            return;
        }
        
        String title = "";
        String subtitle = "";
        
        // 获取基于时间的颜色
        String timeColor = plugin.getConfigManager().getTimeColor(time, totalTime);
        
        if (plugin.getConfigManager().isShowTitle()) {
            title = plugin.colorize(plugin.getConfigManager().getTitleText().replace("{time}", String.valueOf(time)));
        }
        
        if (plugin.getConfigManager().isShowSubtitle()) {
            subtitle = plugin.colorize(plugin.getConfigManager().getSubtitleText()
                    .replace("{time}", String.valueOf(time))
                    .replace("{time_color}", timeColor));
        }
        
        player.sendTitle(
            title,
            subtitle,
            plugin.getConfigManager().getTitleFadeIn(),
            plugin.getConfigManager().getTitleStay(),
            plugin.getConfigManager().getTitleFadeOut()
        );
    }
    
    /**
     * 向玩家发送传送倒计时标题（使用默认总时间）
     * 
     * @param player 玩家
     * @param time 剩余时间（秒）
     */
    public void sendCountdownTitle(Player player, int time) {
        sendCountdownTitle(player, time, plugin.getConfigManager().getSpawnCommandDelay());
    }
    
    /**
     * 向玩家发送传送取消标题
     * 
     * @param player 玩家
     * @param reasonKey 取消原因的键（如"move"、"attack"等）
     */
    public void sendCancelTitle(Player player, String reasonKey) {
        if (!plugin.getConfigManager().isCancelTitleEnabled()) {
            return;
        }
        
        String title = "";
        String subtitle = "";
        String reason = plugin.getConfigManager().getCancelReasonText(reasonKey);
        
        if (plugin.getConfigManager().isShowCancelTitle()) {
            title = plugin.colorize(plugin.getConfigManager().getCancelTitleText());
        }
        
        if (plugin.getConfigManager().isShowCancelSubtitle()) {
            subtitle = plugin.colorize(plugin.getConfigManager().getCancelSubtitleText().replace("{reason}", reason));
        }
        
        player.sendTitle(
            title,
            subtitle,
            plugin.getConfigManager().getCancelTitleFadeIn(),
            plugin.getConfigManager().getCancelTitleStay(),
            plugin.getConfigManager().getCancelTitleFadeOut()
        );
    }
    
    /**
     * 向玩家发送传送成功标题
     * 
     * @param player 玩家
     */
    public void sendSuccessTitle(Player player) {
        if (!plugin.getConfigManager().isSuccessTitleEnabled()) {
            return;
        }
        
        String title = "";
        String subtitle = "";
        
        if (plugin.getConfigManager().isShowSuccessTitle()) {
            title = plugin.colorize(plugin.getConfigManager().getSuccessTitleText());
        }
        
        if (plugin.getConfigManager().isShowSuccessSubtitle()) {
            subtitle = plugin.colorize(plugin.getConfigManager().getSuccessSubtitleText());
        }
        
        player.sendTitle(
            title,
            subtitle,
            plugin.getConfigManager().getSuccessTitleFadeIn(),
            plugin.getConfigManager().getSuccessTitleStay(),
            plugin.getConfigManager().getSuccessTitleFadeOut()
        );
    }
    
    /**
     * 清除玩家的标题
     * 
     * @param player 玩家
     */
    public void clearTitle(Player player) {
        player.resetTitle();
    }
    
    /**
     * 播放开始传送音效
     * 
     * @param player 玩家
     */
    public void playStartSound(Player player) {
        if (plugin.getConfigManager().isStartSoundEnabled()) {
            try {
                player.playSound(
                    player.getLocation(),
                    plugin.getConfigManager().getStartSoundName(),
                    plugin.getConfigManager().getStartSoundVolume(),
                    plugin.getConfigManager().getStartSoundPitch()
                );
            } catch (Exception e) {
                plugin.getLogger().warning("播放开始传送音效时出错: " + e.getMessage());
            }
        }
    }
    
    /**
     * 播放倒计时音效
     * 
     * @param player 玩家
     * @param time 剩余时间（秒）
     * @param totalTime 总倒计时时间（秒）
     */
    public void playCountdownSound(Player player, int time, int totalTime) {
        if (plugin.getConfigManager().isCountdownSoundEnabled()) {
            try {
                // 获取基于时间的音调
                float pitch = plugin.getConfigManager().getTimePitch(time, totalTime);
                
                player.playSound(
                    player.getLocation(),
                    plugin.getConfigManager().getCountdownSoundName(),
                    plugin.getConfigManager().getCountdownSoundVolume(),
                    pitch
                );
            } catch (Exception e) {
                plugin.getLogger().warning("播放倒计时音效时出错: " + e.getMessage());
            }
        }
    }
    
    /**
     * 播放倒计时音效（使用默认总时间）
     * 
     * @param player 玩家
     */
    public void playCountdownSound(Player player) {
        playCountdownSound(player, 1, plugin.getConfigManager().getSpawnCommandDelay());
    }
    
    /**
     * 播放传送成功音效
     * 
     * @param player 玩家
     */
    public void playSuccessSound(Player player) {
        if (plugin.getConfigManager().isSuccessSoundEnabled()) {
            try {
                player.playSound(
                    player.getLocation(),
                    plugin.getConfigManager().getSuccessSoundName(),
                    plugin.getConfigManager().getSuccessSoundVolume(),
                    plugin.getConfigManager().getSuccessSoundPitch()
                );
            } catch (Exception e) {
                plugin.getLogger().warning("播放传送成功音效时出错: " + e.getMessage());
            }
        }
    }
    
    /**
     * 播放传送取消音效
     * 
     * @param player 玩家
     */
    public void playCancelSound(Player player) {
        if (plugin.getConfigManager().isCancelSoundEnabled()) {
            try {
                player.playSound(
                    player.getLocation(),
                    plugin.getConfigManager().getCancelSoundName(),
                    plugin.getConfigManager().getCancelSoundVolume(),
                    plugin.getConfigManager().getCancelSoundPitch()
                );
            } catch (Exception e) {
                plugin.getLogger().warning("播放传送取消音效时出错: " + e.getMessage());
            }
        }
    }
} 