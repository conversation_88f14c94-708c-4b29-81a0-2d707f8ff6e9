package dev.pika.spawnTP;

import dev.pika.spawnTP.commands.SpawnCommand;
import dev.pika.spawnTP.commands.SpawnTPCommand;
import dev.pika.spawnTP.listeners.PlayerInteractionListener;
import dev.pika.spawnTP.listeners.PlayerJoinListener;
import dev.pika.spawnTP.listeners.PlayerMoveListener;
import dev.pika.spawnTP.utils.ConfigManager;
import dev.pika.spawnTP.utils.ConfigUpdater;
import dev.pika.spawnTP.utils.CooldownManager;
import dev.pika.spawnTP.utils.TeleportManager;
import dev.pika.spawnTP.utils.TitleManager;
import org.bukkit.ChatColor;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;

public final class SpawnTP extends JavaPlugin {
    
    private static SpawnTP instance;
    private ConfigManager configManager;
    private CooldownManager cooldownManager;
    private TeleportManager teleportManager;
    private TitleManager titleManager;
    private ConfigUpdater configUpdater;

    @Override
    public void onEnable() {
        // 设置实例
        instance = this;
        
        // 加载配置
        saveDefaultConfig();
        
        // 初始化配置更新器
        configUpdater = new ConfigUpdater(this);
        
        // 更新配置文件（添加新的配置项）
        if (configUpdater.updateConfig()) {
            getLogger().info("配置文件已更新，正在重新加载...");
            
            // 强制重新加载配置文件
            File configFile = new File(getDataFolder(), "config.yml");
            if (configFile.exists()) {
                try {
                    reloadConfig();
                    getLogger().info("配置文件已重新加载。");
                } catch (Exception e) {
                    getLogger().severe("重新加载配置文件时出错: " + e.getMessage());
                }
            }
        }
        
        // 初始化配置管理器
        configManager = new ConfigManager(this);
        
        // 初始化其他管理器
        cooldownManager = new CooldownManager();
        titleManager = new TitleManager(this);
        teleportManager = new TeleportManager(this);
        
        // 注册事件监听器
        getServer().getPluginManager().registerEvents(new PlayerJoinListener(this), this);
        getServer().getPluginManager().registerEvents(new PlayerMoveListener(this), this);
        getServer().getPluginManager().registerEvents(new PlayerInteractionListener(this), this);
        
        // 注册命令
        getCommand("spawntp").setExecutor(new SpawnTPCommand(this));
        getCommand("spawntp").setTabCompleter((SpawnTPCommand) getCommand("spawntp").getExecutor());
        getCommand("spawn").setExecutor(new SpawnCommand(this));
        
        getLogger().info("SpawnTP 插件已启用!");
    }

    @Override
    public void onDisable() {
        getLogger().info("SpawnTP 插件已禁用!");
    }
    
    @Override
    public void reloadConfig() {
        getLogger().info("正在重新加载配置文件...");
        
        // 先更新配置文件（添加新的配置项）
        if (configUpdater != null && configUpdater.updateConfig()) {
            getLogger().info("配置文件已更新，包含新的配置项。");
        }
        
        // 调用父类方法重载配置
        super.reloadConfig();
        
        // 重新初始化配置管理器
        configManager = new ConfigManager(this);
        
        getLogger().info("配置文件重新加载完成。");
    }
    
    public static SpawnTP getInstance() {
        return instance;
    }
    
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    public CooldownManager getCooldownManager() {
        return cooldownManager;
    }
    
    public TeleportManager getTeleportManager() {
        return teleportManager;
    }
    
    public TitleManager getTitleManager() {
        return titleManager;
    }
    
    public String colorize(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }
}
