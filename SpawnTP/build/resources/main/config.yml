# SpawnTP 配置文件

# 可用变量说明:
# {time} - 倒计时剩余时间（秒），用于标题和消息中
# {delay} - 设置的延迟时间（秒），用于消息中
# {reason} - 传送取消的原因，用于取消标题中
# {time_color} - 倒计时颜色渐变，会根据剩余时间自动变化
# 
# 取消原因变量:
# - move: 玩家移动
# - attack: 玩家攻击
# - block-place: 玩家放置方块
# - block-break: 玩家破坏方块
# - damage: 玩家受到伤害
# - interact: 玩家与物品交互

# 传送设置
spawn:
  # 是否启用进入服务器时传送
  enabled: true
  # 传送坐标
  location:
    world: 'world'
    x: 0.5
    y: 120.0
    z: 0.5
    yaw: 0.0
    pitch: 0.0
  # 是否在传送时显示消息通知给玩家
  show-teleport-message: true
  # 是否在玩家加入服务器时显示传送消息
  show-join-message: false
  
  # Spawn命令设置
  command:
    # 是否启用/spawn命令
    enabled: true
    # 传送前等待时间（秒）
    delay: 3
    # 命令冷却时间（秒）
    cooldown: 30
    # 是否在使用/spawn命令时显示消息
    show-message: true
    # 移动取消传送
    cancel-on-move: true
    # 取消传送的条件
    cancel-on:
      # 是否在攻击时取消传送
      attack: true
      # 是否在放置方块时取消传送
      block-place: true
      # 是否在破坏方块时取消传送
      block-break: true
      # 是否在受到伤害时取消传送
      damage: true
      # 是否在交互物品时取消传送
      interact: true
    
    # 标题提示设置
    title:
      # 是否启用标题提示
      enabled: true
      # 是否显示主标题
      show-title: true
      # 是否显示副标题
      show-subtitle: true
      # 主标题文本 (使用 {time} 表示剩余时间)
      text: '&b传送中...'
      # 副标题文本 (使用 {time} 表示剩余时间, {time_color} 表示渐变颜色)
      subtitle: '&e请等待 {time_color}{time} &e秒'
      # 淡入时间（tick）
      fade-in: 5
      # 停留时间（tick）
      stay: 20
      # 淡出时间（tick）
      fade-out: 5
      # 倒计时颜色渐变设置
      countdown-color:
        # 是否启用倒计时颜色渐变
        enabled: true
        # 开始颜色 (当倒计时刚开始时)
        start-color: '&b'
        # 结束颜色 (当倒计时即将结束时)
        end-color: '&c'
      # 传送取消标题设置
      cancel:
        # 是否显示传送取消标题
        enabled: true
        # 是否显示主标题
        show-title: true
        # 是否显示副标题
        show-subtitle: true
        # 主标题文本 (使用 {reason} 表示取消原因)
        text: '&c传送已取消!'
        # 副标题文本 (使用 {reason} 表示取消原因)
        subtitle: '&7原因: &f{reason}'
        # 淡入时间（tick）
        fade-in: 5
        # 停留时间（tick）
        stay: 40
        # 淡出时间（tick）
        fade-out: 5
      # 传送成功标题设置
      success:
        # 是否显示传送成功标题
        enabled: true
        # 是否显示主标题
        show-title: true
        # 是否显示副标题
        show-subtitle: true
        # 主标题文本
        text: '&a传送成功!'
        # 副标题文本
        subtitle: '&7你已被传送到出生点'
        # 淡入时间（tick）
        fade-in: 5
        # 停留时间（tick）
        stay: 30
        # 淡出时间（tick）
        fade-out: 5
    
    # 音效设置
    sounds:
      # 是否启用音效
      enabled: true
      # 开始传送时的音效
      start:
        enabled: true
        sound: 'entity.enderman.teleport'
        volume: 1.0
        pitch: 0.5
      # 倒计时音效
      countdown:
        enabled: true
        sound: 'block.note_block.pling'
        volume: 1.0
        # 基础音调
        pitch: 1.0
        # 音调变化设置
        pitch-change:
          # 是否启用音调变化
          enabled: true
          # 开始音调 (当倒计时刚开始时)
          start-pitch: 0.8
          # 结束音调 (当倒计时即将结束时)
          end-pitch: 2.0
      # 传送成功音效
      success:
        enabled: true
        sound: 'entity.player.levelup'
        volume: 1.0
        pitch: 1.0
      # 传送取消音效
      cancel:
        enabled: true
        sound: 'entity.villager.no'
        volume: 1.0
        pitch: 1.0
  
# 权限设置
permissions:
  # 拥有此权限的玩家不会被自动传送
  bypass: 'spawntp.bypass'
  # 拥有此权限的玩家可以绕过/spawn命令的冷却时间
  bypass-cooldown: 'spawntp.bypass.cooldown'
  # 拥有此权限的玩家可以绕过/spawn命令的延迟时间
  bypass-delay: 'spawntp.bypass.delay'

# 世界设置
worlds:
  # 是否只在特定世界启用传送
  specific-world-only: true
  # 启用传送的世界列表（如果specific-world-only为true）
  enabled-worlds:
    - 'world'

# 消息设置
messages:
  prefix: '&8[&bSpawnTP&8] &7'
  teleported: '&a你已被传送到出生点!'
  no-permission: '&c你没有权限执行此命令!'
  spawn-set: '&a出生点位置已成功设置!'
  config-reloaded: '&a配置文件已重新加载!'
  plugin-enabled: '&a自动传送功能已启用!'
  plugin-disabled: '&c自动传送功能已禁用!'
  spawn-command-disabled: '&c/spawn命令已被禁用!'
  spawn-teleporting: '&e你将在&b{delay}&e秒后传送到出生点...'
  spawn-teleported: '&a你已被传送到出生点!'
  spawn-cooldown: '&c你需要等待&b{time}&c秒才能再次使用此命令!'
  spawn-cancelled: '&c你移动了，传送已取消!'
  spawn-cancelled-attack: '&c你进行了攻击，传送已取消!'
  spawn-cancelled-block-place: '&c你放置了方块，传送已取消!'
  spawn-cancelled-block-break: '&c你破坏了方块，传送已取消!'
  spawn-cancelled-damage: '&c你受到了伤害，传送已取消!'
  spawn-cancelled-interact: '&c你与物品交互，传送已取消!'
  # 取消原因（用于标题显示）
  reason-move: '移动'
  reason-attack: '攻击'
  reason-block-place: '放置方块'
  reason-block-break: '破坏方块'
  reason-damage: '受到伤害'
  reason-interact: '物品交互'

# 可用的音效列表（仅供参考）
# 这些是一些常用的音效，您可以在此处查找更多：https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/Sound.html
sound-reference:
  # 传送相关
  teleport:
    - 'entity.enderman.teleport'
    - 'entity.shulker.teleport'
  # 提示音
  notification:
    - 'block.note_block.pling'
    - 'entity.experience_orb.pickup'
    - 'block.note_block.bell'
    - 'block.note_block.chime'
  # 成功音效
  success:
    - 'entity.player.levelup'
    - 'ui.toast.challenge_complete'
    - 'block.beacon.activate'
  # 失败/取消音效
  failure:
    - 'entity.villager.no'
    - 'block.note_block.bass'
    - 'entity.item.break'
    - 'block.anvil.land' 