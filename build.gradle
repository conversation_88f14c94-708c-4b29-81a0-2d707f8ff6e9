apply plugin: 'java'

group = 'dev.pika'
version = '0.1'

repositories {
    mavenCentral()
    maven {
        url "https://libraries.minecraft.net"
    }
}

dependencies {
    compileOnly("net.md-5:bungeecord-api:1.21-R0.3")
}


def targetJavaVersion = 8
java {
    def javaVersion = JavaVersion.toVersion(targetJavaVersion)
    sourceCompatibility = javaVersion
    targetCompatibility = javaVersion
    if (JavaVersion.current() < javaVersion) {
        toolchain.languageVersion = JavaLanguageVersion.of(targetJavaVersion)
    }
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'

    if (targetJavaVersion >= 10 || JavaVersion.current().isJava10Compatible()) {
        options.release.set(targetJavaVersion)
    }
}

processResources {
    def props = [version: version]
    inputs.properties props
    expand props
    filteringCharset 'UTF-8'
    filesMatching('bungee.yml') {
        expand props
    }
}
